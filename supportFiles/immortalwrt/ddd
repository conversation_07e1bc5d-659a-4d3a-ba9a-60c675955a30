#!/bin/bash

show_menu() {
    clear
    echo "====================================="
    echo "  Immortalwrt Installer by m2kall"
    echo "====================================="
    echo "1. Install Immortalwrt 24.10.2 (1GB)"
    echo "0. Quit"
    echo "====================================="
}

detect_disk() {
    # Get all available disks
    local disks=($(lsblk -d -n -o NAME,RO,TYPE | awk '$3 == "disk" && $2 == "0" {print "/dev/"$1}'))

    # Error handling
    if [ ${#disks[@]} -eq 0 ]; then
        echo "Error: No available disk devices detected!" >&2
        exit 1
    fi

    # Display disk list with sizes
    echo "Available disks:" >&2
    for i in "${!disks[@]}"; do
        size=$(lsblk -d -n -b -o SIZE ${disks[i]} | awk '{printf "%.2f GB", $1/1000000000}')
        printf "%2d. %-12s %8s\n" $((i+1)) "${disks[i]}" "$size" >&2
    done

    # User selection logic
    while true; do
        read -p "Select target disk number (1-${#disks[@]}): " choice
        if [[ "$choice" =~ ^[0-9]+$ ]] && (( choice >= 1 && choice <= ${#disks[@]} )); then
            selected_disk="${disks[choice-1]}"
            echo "[Security Notice] Selected disk: $selected_disk" >&2
            echo "$selected_disk"
            return
        else
            echo "Invalid input. Please enter a valid number between 1-${#disks[@]}"
        fi
    done
}

confirm_danger() {
    local target_disk=$1
    local image_file=$2
    
    echo "!! DANGEROUS OPERATION CONFIRMATION !!"
    echo "──────────────────────────────────────"
    echo "Target device: $target_disk"
    echo "Image file:    $image_file"
    echo "──────────────────────────────────────"
    echo "This will ERASE ALL DATA on $target_disk!"
    read -p "Confirm write operation? (Type uppercase YES to proceed): " confirm

    if [ "$confirm" != "YES" ]; then
        echo "Operation cancelled"
        exit 0
    fi

}

install_system() {
    local image_name=$1
    local image_file="/mnt/$image_name"
    local target_disk
    
    # Get user-selected disk
    target_disk=$(detect_disk)
    
    # Display disk information
    echo -e "\nDisk Information:"
    fdisk -l "$target_disk" | grep Disk | head -1
    
    # Check image file existence
    if [ ! -f "$image_file" ]; then
        echo -e "\nError: Image file $image_file not found!"
        echo "Please:"
        echo "1. Place the image file in /mnt directory"
        echo "2. Verify file permissions"
        exit 1
    fi
    
    # Final confirmation
    confirm_danger "$target_disk" "$image_file"
    
    echo -e "\nStarting system write... (Press Ctrl+C to cancel)"
    sleep 2
    
    # Perform write operation
    dd if="$image_file" of="$target_disk" bs=4M conv=fsync status=progress
    
    echo "──────────────────────────────────────"
    echo "Immortalwrt installed successfully:"
}

while true; do
    show_menu
    read -p "Enter your choice [0-1]: " choice
    
    case $choice in
        1)
            install_system "immortalwrt.img"
            break
            ;;
        0)
            echo "Exiting installer"
            exit 0
            ;;
        *)
            echo "Invalid option, please try again"
            sleep 2
            ;;
    esac
done